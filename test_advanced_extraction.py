#!/usr/bin/env python3
"""
Test script to verify the updated Advanced Guidelines obligation extraction system
"""

import json
import os
from ToolsetForPolicyReview import analyze_contract_for_obligations

def test_advanced_extraction():
    """Test the advanced obligation extraction with a sample contract text"""
    
    # Sample contract text for testing
    sample_contract = """
    MASTER SERVICE AGREEMENT
    
    This Master Service Agreement ("Agreement") is entered into between Company A and Company B.
    
    SECTION 5. SERVICE DELIVERY OBLIGATIONS
    5.1 The Service Provider shall deliver monthly performance reports by the 5th business day of each month.
    5.2 The Service Provider shall maintain 99.5% system availability as measured monthly.
    5.3 The Service Provider shall respond to critical incidents within 2 hours of notification.
    
    SECTION 8. FINANCIAL OBLIGATIONS  
    8.1 The Client shall pay invoices within 30 days of receipt.
    8.2 The Service Provider shall provide quarterly cost savings reports.
    8.3 All pricing shall be subject to annual benchmarking reviews.
    
    SECTION 12. COMPLIANCE REQUIREMENTS
    12.1 The Service Provider shall maintain ISO 27001 certification throughout the term.
    12.2 The Service Provider shall conduct annual security audits and provide results to Client.
    12.3 All personnel shall undergo background verification before accessing Client systems.
    
    SECTION 15. BUSINESS CONTINUITY
    15.1 The Service Provider shall maintain a disaster recovery plan with RTO of 4 hours.
    15.2 The Service Provider shall test the disaster recovery plan annually.
    """
    
    print("Testing Advanced Guidelines Obligation Extraction...")
    print("=" * 60)
    
    # Check if Advanced_guidelines.json exists
    if not os.path.exists("Advanced_guidelines.json"):
        print("❌ ERROR: Advanced_guidelines.json not found!")
        return False
    
    try:
        # Test the extraction
        result = analyze_contract_for_obligations(
            contract_text=sample_contract,
            guidelines_path="Advanced_guidelines.json",
            save_to_file=True,
            output_dir="test_output"
        )
        
        # Parse the result
        print(f"Raw result: {result}")
        result_data = json.loads(result)

        if result_data.get("success"):
            obligations = result_data.get("data", {}).get("result", [])

            print(f"✅ SUCCESS: Extracted {len(obligations)} obligations")
            print("\nSample extracted obligations:")
            print("-" * 40)

            for i, obligation in enumerate(obligations[:3], 1):  # Show first 3
                print(f"\n{i}. {obligation.get('obligation_title', 'N/A')}")
                print(f"   Category: {obligation.get('category', 'N/A')}")
                print(f"   Subcategory: {obligation.get('subcategory', 'N/A')}")
                print(f"   Priority: {obligation.get('priority', 'N/A')}")
                print(f"   Reference: {obligation.get('reference_clause', 'N/A')}")

            if len(obligations) > 3:
                print(f"\n... and {len(obligations) - 3} more obligations")

            # Verify required fields are present
            required_fields = [
                'category', 'subcategory', 'obligation_title',
                'short_description', 'detailed_summary', 'reference_clause',
                'impact_risk', 'priority'
            ]

            missing_fields = []
            for obligation in obligations:
                for field in required_fields:
                    if field not in obligation:
                        missing_fields.append(field)

            if missing_fields:
                print(f"\n⚠️  WARNING: Some obligations missing fields: {set(missing_fields)}")
            else:
                print("\n✅ All required fields present in extracted obligations")

            return True

        else:
            print(f"❌ FAILED: {result_data.get('message', 'Unknown error')}")
            print(f"Full result data: {result_data}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        print(f"Full result: {result}")
        return False

def verify_advanced_guidelines():
    """Verify the Advanced_guidelines.json structure"""
    
    print("\nVerifying Advanced_guidelines.json structure...")
    print("-" * 40)
    
    try:
        with open("Advanced_guidelines.json", "r") as f:
            guidelines = json.load(f)
        
        # Check required sections
        required_sections = [
            'framework_metadata', 'obligation_categories', 
            'risk_criteria', 'exclusions', 'priority_matrix', 
            'extraction_guidelines'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in guidelines:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ Missing sections: {missing_sections}")
            return False
        
        # Check categories
        categories = guidelines.get('obligation_categories', [])
        print(f"✅ Found {len(categories)} obligation categories")
        
        # Check exclusions
        exclusions = guidelines.get('exclusions', [])
        print(f"✅ Found {len(exclusions)} exclusion criteria")
        
        # Check risk criteria
        risks = guidelines.get('risk_criteria', [])
        print(f"✅ Found {len(risks)} risk criteria")
        
        print("✅ Advanced_guidelines.json structure is valid")
        return True
        
    except Exception as e:
        print(f"❌ ERROR reading Advanced_guidelines.json: {str(e)}")
        return False

if __name__ == "__main__":
    print("Advanced Guidelines Obligation Extraction Test")
    print("=" * 50)
    
    # Create test output directory
    os.makedirs("test_output", exist_ok=True)
    
    # Verify guidelines file
    guidelines_ok = verify_advanced_guidelines()
    
    if guidelines_ok:
        # Test extraction
        extraction_ok = test_advanced_extraction()
        
        if extraction_ok:
            print("\n🎉 ALL TESTS PASSED!")
            print("The advanced obligation extraction system is working correctly.")
        else:
            print("\n❌ EXTRACTION TEST FAILED!")
    else:
        print("\n❌ GUIDELINES VERIFICATION FAILED!")
    
    print("\nTest completed.")
