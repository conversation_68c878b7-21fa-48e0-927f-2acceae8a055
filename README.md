# 🤖 Agentic Contract Analysis System

A pure agentic contract analysis system where AI agents intelligently decide what tasks to perform based on user selections.

## 📁 Essential Files

### **Core System Files:**
- `streamlit_app.py` - Main frontend interface
- `AgentForContractReview.py` - AI Agent system
- `ToolsetForPolicyReview.py` - Analysis functions
- `utils_fr_PolicyReview.py` - Utility functions

### **Configuration Files:**
- `Advanced_guidelines.json` - Default EYGS SRM D&O Framework
- `contract_templates.json` - Contract templates
- `requirements.txt` - Python dependencies

### **Sample Files:**
- `2022 Lenses SOW - Fully Executed 1.pdf` - Sample contract
- `Sample_Contract.txt` - Sample text contract

## 🚀 How to Run

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the frontend:**
   ```bash
   streamlit run streamlit_app.py
   ```

3. **Or run the agent directly:**
   ```bash
   python AgentForContractReview.py
   ```

## 🎯 How It Works

### **Frontend Flow:**
1. Upload contract file (PDF/TXT)
2. Enter template type (MSA, NDA, etc.)
3. Select analysis tasks:
   - 📋 Template Extraction
   - ⚖️ Obligations Analysis
   - 📊 Table Extraction
4. Choose guidelines (default recommended)
5. Agent performs selected tasks intelligently

### **Agent Intelligence:**
- Receives natural language request
- Decides which tools to call
- Executes only selected tasks
- Provides comprehensive summary

## 🔧 Key Features

- ✅ **Pure Agentic**: Agent decides what to do
- ✅ **User Control**: Select exactly what you want
- ✅ **Live Actions**: See what agent is doing in real-time
- ✅ **Advanced Framework**: Comprehensive obligation analysis
- ✅ **Flexible**: Easy to extend and modify

## 📊 Analysis Types

1. **Template Extraction**: Extract key contract fields
2. **Obligations Analysis**: Analyze contractual obligations using EYGS framework
3. **Table Extraction**: Extract tables and structured data (PDF only)

The system is designed to be simple, powerful, and truly agentic! 🎉
