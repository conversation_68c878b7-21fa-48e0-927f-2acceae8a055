import os,json,time
from azure.ai.projects.models import FunctionTool, ToolSet, CodeInterpreterTool
from utils_fr_PolicyReview import _initialize_AIAgentProject_client,_extract_data_from_file

# --- Step 1: Import Your Custom Tool ---
try:
    from ToolsetForPolicyReview import (analyze_contract_for_obligations,
                                       extract_fields_using_template,
                                        analyze_document_and_extract_tables)
    
except ImportError:
    print("Error: Could not import 'analyze_contract_for_obligations' from 'ToolsetForPolicyReview.py'.")
    print("Please ensure the file exists and contains your function.")
    exit()




# Configuration for agent (paths will be provided dynamically by user)
CONFIG = {
    "AGENT_NAME": "multi-tool-contract-analysis-agent",
    "POLLING_INTERVAL": 5
}

project_client,model_deployment_name = _initialize_AIAgentProject_client()



# --- Step 3: Main Agent Logic ---
with project_client:
    # Define a dictionary to map tool names to their functions
    available_tools = {
        "analyze_contract_for_obligations": analyze_contract_for_obligations,
        "extract_fields_using_template": extract_fields_using_template,
        "analyze_document_and_extract_tables":analyze_document_and_extract_tables
    }

    all_functions_tool = FunctionTool(functions=[analyze_contract_for_obligations, extract_fields_using_template,analyze_document_and_extract_tables])
    
    # Initialize the toolset and add the single FunctionTool instance.
    toolset = ToolSet()
    toolset.add(all_functions_tool)

# ------------------------- Old Agent Instructions ---------------------------- # 
#     agent_instructions = """
#    You are a multi-skilled legal contract analysis assistant. You have two tools at your disposal.
#     1. `analyze_contract_for_obligations`: Use this tool when the user asks for a general "policy review" or to "extract all obligations" based on the internal framework.
#     2. `extract_fields_using_template`: Use this tool  when the user explicitly mentions a specific template name (like "MSA", "NDA", "Master Service Agreement" or others) and asks to extract fields from the contract text based on template. You must pass the user's template name as the 'template_query' argument.
#     3. `analyze_document_and_extract_tables`: Use this tool to extract tables from contract and dump the data into excel file.
#     Carefully analyze the user's request to choose the correct tool. Your final response should be the direct JSON output from the tool you use.
#     """

    
    # ---------------------------- New Instructions ---------------------- #
    agent_instructions = """
                        You are an expert legal contract analysis assistant specialized in the EYGS SRM Function D&O Framework. You follow a strict three-step process for comprehensive contract analysis.

                        TOOL USAGE AND PARAMETERS:
                        - `extract_fields_using_template`: Use this tool first. It requires the 'contract_text' and 'template_query'. Extract these values from the user's prompt. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
                        - `analyze_contract_for_obligations`: Use this tool second for advanced obligation extraction. It requires the 'contract_text' and 'guidelines_path'. Extract these values from the user's prompt. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
                        - `analyze_document_and_extract_tables`: Use this tool last. It requires the 'pdf_path'. Extract this value from the user's prompt. To save the output, ensure you call it with `save_to_excel=True` and `save_to_json=True`.

                        EXECUTION STRATEGY:
                        1. First, call `extract_fields_using_template` with the contract_text and template_query from user input to extract contract metadata and key fields.
                        2. Second, call `analyze_contract_for_obligations` with the contract_text and guidelines_path from user input to perform comprehensive obligation extraction.
                        3. Third, call `analyze_document_and_extract_tables` with the pdf_path from user input to extract all tables and structured data.

                        FRAMEWORK DETAILS:
                        The Advanced_guidelines.json contains the EYGS SRM Function D&O Framework with:
                        - 5 main obligation categories (Service Delivery, Performance, Commercial/Financial, Compliance, Relationship Management)
                        - Detailed subcategories with specific codes
                        - Risk classification and priority matrix
                        - Comprehensive exclusion criteria
                        - Structured output format requirements

                        IMPORTANT: Your final response to the user should be a summary message confirming that all three analyses were completed using the advanced framework and the files were saved.

                        """

    
    print(f"Creating agent: '{ CONFIG['AGENT_NAME'] }'...")
    agent = project_client.agents.create_agent(
        model=model_deployment_name,
        name=CONFIG['AGENT_NAME'],
        instructions=agent_instructions,
        toolset=toolset,
    )
    print(f"Agent created, ID: {agent.id}")


    thread = project_client.agents.create_thread()
    print(f"Created thread, ID: {thread.id}")

    print("\n" + "="*60)
    print("AGENT READY FOR DYNAMIC INPUT")
    print("="*60)
    print("The agent is now ready to accept user prompts with dynamic file paths.")
    print("Expected user prompt format:")
    print("""
    Please perform a comprehensive analysis on the provided contract.

    INPUTS:
    - PDF Path for table extraction: [USER_PROVIDED_PATH]
    - Guidelines Path for obligation analysis: [USER_PROVIDED_PATH]
    - Contract Type for template extraction: [USER_PROVIDED_TYPE]
    - Full Contract Text: [USER_PROVIDED_TEXT]

    Please execute all three analysis steps.
    """)
    print("="*60)

    # Wait for user input instead of using hardcoded values
    print("\nWaiting for user input...")
    print("You can now send a message to the agent with your specific file paths and contract details.")


    # The agent is now ready to receive dynamic user inputs
    # Users can interact with the agent by providing their specific:
    # - Contract file paths
    # - Guidelines file paths
    # - Template types
    # - Contract text content

    # Example of how to interact with the agent programmatically:
    #
    # user_message = """
    # Please perform a comprehensive analysis on the provided contract.
    #
    # INPUTS:
    # - PDF Path for table extraction: /path/to/contract.pdf
    # - Guidelines Path for obligation analysis: Advanced_guidelines.json
    # - Contract Type for template extraction: Master Service Agreement
    # - Full Contract Text: [contract content here]
    #
    # Please execute all three analysis steps.
    # """
    #
    # message = project_client.agents.create_message(
    #     thread_id=thread.id,
    #     role="user",
    #     content=user_message,
    # )
    #
    # run = project_client.agents.create_run(thread_id=thread.id, assistant_id=agent.id)
    #
    # # Then handle the run execution with tool calls...
        run = project_client.agents.get_run(thread_id=thread.id, run_id=run.id)
        print(f"Polling run status: {run.status}")

    # Process the final results of the run
    if run.status == "failed":
        print(f"\n--- ❌ Run Failed ---")
        if run.last_error:
            print(f"Error Code: {run.last_error.code}")
            print(f"Error Message: {run.last_error.message}")
        print("---------------------\n")
    elif run.status == "completed":
        print("\nFetching final conversation messages...")
        messages = project_client.agents.list_messages(thread_id=thread.id)
        
        # The assistant's final message will now contain the result
        assistant_response = None
        for msg in reversed(messages.data):
            if msg.role == "assistant" and msg.content:
                assistant_response = msg.content[0].text.value
                break
        
        print("\n--- ✅ Agent Extraction Complete ---")
        if assistant_response:
            try:
                # The final response from the agent should be the JSON from our tool.
                parsed_json = json.loads(assistant_response)
                print(json.dumps(parsed_json, indent=4))
            except json.JSONDecodeError:
                print("Final response was not valid JSON. Raw output from assistant:")
                print(assistant_response)
        else:
            print("No final response from the assistant was found.")
        print("----------------------------------\n")
    else:
        print(f"Run ended with an unexpected status: {run.status}")


