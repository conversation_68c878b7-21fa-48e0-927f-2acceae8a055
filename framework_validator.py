"""
Framework Validator for Advanced Guidelines
Validates that custom uploaded guidelines match the required structure of Advanced_guidelines.json
"""

import json
from typing import Dict, <PERSON>, Tuple, Any


def validate_framework_structure(uploaded_guidelines: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validates that uploaded guidelines match the Advanced_guidelines.json structure
    
    Args:
        uploaded_guidelines: Dictionary containing the uploaded guidelines
        
    Returns:
        Tuple of (is_valid: bool, errors: List[str])
    """
    errors = []
    
    # Required top-level sections
    required_sections = [
        'framework_metadata',
        'obligation_categories', 
        'risk_criteria',
        'exclusions',
        'priority_matrix',
        'extraction_guidelines'
    ]
    
    # Check top-level structure
    for section in required_sections:
        if section not in uploaded_guidelines:
            errors.append(f"Missing required section: '{section}'")
    
    if errors:
        return False, errors
    
    # Validate framework_metadata
    metadata_errors = _validate_metadata(uploaded_guidelines.get('framework_metadata', {}))
    errors.extend(metadata_errors)
    
    # Validate obligation_categories
    categories_errors = _validate_obligation_categories(uploaded_guidelines.get('obligation_categories', []))
    errors.extend(categories_errors)
    
    # Validate risk_criteria
    risk_errors = _validate_risk_criteria(uploaded_guidelines.get('risk_criteria', []))
    errors.extend(risk_errors)
    
    # Validate exclusions
    exclusions_errors = _validate_exclusions(uploaded_guidelines.get('exclusions', []))
    errors.extend(exclusions_errors)
    
    # Validate priority_matrix
    priority_errors = _validate_priority_matrix(uploaded_guidelines.get('priority_matrix', {}))
    errors.extend(priority_errors)
    
    # Validate extraction_guidelines
    extraction_errors = _validate_extraction_guidelines(uploaded_guidelines.get('extraction_guidelines', {}))
    errors.extend(extraction_errors)
    
    return len(errors) == 0, errors


def _validate_metadata(metadata: Dict[str, Any]) -> List[str]:
    """Validate framework_metadata section"""
    errors = []
    required_fields = ['version', 'description', 'purpose']
    
    for field in required_fields:
        if field not in metadata:
            errors.append(f"framework_metadata missing required field: '{field}'")
        elif not isinstance(metadata[field], str) or not metadata[field].strip():
            errors.append(f"framework_metadata.{field} must be a non-empty string")
    
    return errors


def _validate_obligation_categories(categories: List[Dict[str, Any]]) -> List[str]:
    """Validate obligation_categories section"""
    errors = []
    
    if not isinstance(categories, list):
        errors.append("obligation_categories must be a list")
        return errors
    
    if len(categories) == 0:
        errors.append("obligation_categories cannot be empty")
        return errors
    
    required_category_fields = [
        'category_name', 'category_code', 'category_description', 
        'mandatory_tracking', 'subcategories', 'risk_association'
    ]
    
    for i, category in enumerate(categories):
        if not isinstance(category, dict):
            errors.append(f"obligation_categories[{i}] must be a dictionary")
            continue
            
        # Check required fields
        for field in required_category_fields:
            if field not in category:
                errors.append(f"obligation_categories[{i}] missing required field: '{field}'")
        
        # Validate subcategories
        if 'subcategories' in category:
            subcategory_errors = _validate_subcategories(category['subcategories'], i)
            errors.extend(subcategory_errors)
    
    return errors


def _validate_subcategories(subcategories: List[Dict[str, Any]], category_index: int) -> List[str]:
    """Validate subcategories within a category"""
    errors = []
    
    if not isinstance(subcategories, list):
        errors.append(f"obligation_categories[{category_index}].subcategories must be a list")
        return errors
    
    required_subcategory_fields = [
        'subcategory_name', 'subcategory_code', 'description', 'keywords'
    ]
    
    for j, subcategory in enumerate(subcategories):
        if not isinstance(subcategory, dict):
            errors.append(f"obligation_categories[{category_index}].subcategories[{j}] must be a dictionary")
            continue
            
        for field in required_subcategory_fields:
            if field not in subcategory:
                errors.append(f"obligation_categories[{category_index}].subcategories[{j}] missing required field: '{field}'")
        
        # Validate keywords is a list
        if 'keywords' in subcategory and not isinstance(subcategory['keywords'], list):
            errors.append(f"obligation_categories[{category_index}].subcategories[{j}].keywords must be a list")
    
    return errors


def _validate_risk_criteria(risk_criteria: List[Dict[str, Any]]) -> List[str]:
    """Validate risk_criteria section"""
    errors = []
    
    if not isinstance(risk_criteria, list):
        errors.append("risk_criteria must be a list")
        return errors
    
    required_risk_fields = ['risk_name', 'risk_code', 'description', 'priority_weight', 'mandatory_categories']
    
    for i, risk in enumerate(risk_criteria):
        if not isinstance(risk, dict):
            errors.append(f"risk_criteria[{i}] must be a dictionary")
            continue
            
        for field in required_risk_fields:
            if field not in risk:
                errors.append(f"risk_criteria[{i}] missing required field: '{field}'")
        
        # Validate mandatory_categories is a list
        if 'mandatory_categories' in risk and not isinstance(risk['mandatory_categories'], list):
            errors.append(f"risk_criteria[{i}].mandatory_categories must be a list")
    
    return errors


def _validate_exclusions(exclusions: List[Dict[str, Any]]) -> List[str]:
    """Validate exclusions section"""
    errors = []
    
    if not isinstance(exclusions, list):
        errors.append("exclusions must be a list")
        return errors
    
    required_exclusion_fields = ['exclusion_name', 'exclusion_code', 'description', 'keywords', 'examples']
    
    for i, exclusion in enumerate(exclusions):
        if not isinstance(exclusion, dict):
            errors.append(f"exclusions[{i}] must be a dictionary")
            continue
            
        for field in required_exclusion_fields:
            if field not in exclusion:
                errors.append(f"exclusions[{i}] missing required field: '{field}'")
        
        # Validate keywords and examples are lists
        if 'keywords' in exclusion and not isinstance(exclusion['keywords'], list):
            errors.append(f"exclusions[{i}].keywords must be a list")
        if 'examples' in exclusion and not isinstance(exclusion['examples'], list):
            errors.append(f"exclusions[{i}].examples must be a list")
    
    return errors


def _validate_priority_matrix(priority_matrix: Dict[str, Any]) -> List[str]:
    """Validate priority_matrix section"""
    errors = []
    
    if not isinstance(priority_matrix, dict):
        errors.append("priority_matrix must be a dictionary")
        return errors
    
    required_priorities = ['Critical', 'High', 'Medium', 'Low']
    required_priority_fields = ['description', 'timeline', 'escalation']
    
    for priority in required_priorities:
        if priority not in priority_matrix:
            errors.append(f"priority_matrix missing required priority level: '{priority}'")
        else:
            priority_data = priority_matrix[priority]
            if not isinstance(priority_data, dict):
                errors.append(f"priority_matrix.{priority} must be a dictionary")
                continue
                
            for field in required_priority_fields:
                if field not in priority_data:
                    errors.append(f"priority_matrix.{priority} missing required field: '{field}'")
    
    return errors


def _validate_extraction_guidelines(extraction_guidelines: Dict[str, Any]) -> List[str]:
    """Validate extraction_guidelines section"""
    errors = []
    
    if not isinstance(extraction_guidelines, dict):
        errors.append("extraction_guidelines must be a dictionary")
        return errors
    
    required_sections = ['identification_criteria', 'mandatory_fields', 'output_format']
    
    for section in required_sections:
        if section not in extraction_guidelines:
            errors.append(f"extraction_guidelines missing required section: '{section}'")
    
    # Validate identification_criteria is a list
    if 'identification_criteria' in extraction_guidelines:
        if not isinstance(extraction_guidelines['identification_criteria'], list):
            errors.append("extraction_guidelines.identification_criteria must be a list")
    
    # Validate mandatory_fields is a list
    if 'mandatory_fields' in extraction_guidelines:
        if not isinstance(extraction_guidelines['mandatory_fields'], list):
            errors.append("extraction_guidelines.mandatory_fields must be a list")
    
    # Validate output_format is a dictionary
    if 'output_format' in extraction_guidelines:
        if not isinstance(extraction_guidelines['output_format'], dict):
            errors.append("extraction_guidelines.output_format must be a dictionary")
    
    return errors


def validate_uploaded_guidelines_file(file_content: str) -> Tuple[bool, List[str], Dict[str, Any]]:
    """
    Validate uploaded guidelines file content
    
    Args:
        file_content: String content of uploaded JSON file
        
    Returns:
        Tuple of (is_valid: bool, errors: List[str], parsed_data: Dict)
    """
    try:
        # Parse JSON
        guidelines_data = json.loads(file_content)
        
        # Validate structure
        is_valid, errors = validate_framework_structure(guidelines_data)
        
        return is_valid, errors, guidelines_data
        
    except json.JSONDecodeError as e:
        return False, [f"Invalid JSON format: {str(e)}"], {}
    except Exception as e:
        return False, [f"Error validating guidelines: {str(e)}"], {}


def get_framework_requirements_summary() -> str:
    """
    Returns a summary of the required framework structure for user reference
    """
    return """
    Required Framework Structure:
    
    ✅ framework_metadata: {version, description, purpose}
    ✅ obligation_categories: [
        {category_name, category_code, category_description, mandatory_tracking, 
         subcategories: [{subcategory_name, subcategory_code, description, keywords}],
         risk_association}
    ]
    ✅ risk_criteria: [
        {risk_name, risk_code, description, priority_weight, mandatory_categories}
    ]
    ✅ exclusions: [
        {exclusion_name, exclusion_code, description, keywords, examples}
    ]
    ✅ priority_matrix: {
        Critical: {description, timeline, escalation},
        High: {description, timeline, escalation},
        Medium: {description, timeline, escalation},
        Low: {description, timeline, escalation}
    }
    ✅ extraction_guidelines: {
        identification_criteria: [],
        mandatory_fields: [],
        output_format: {}
    }
    """
