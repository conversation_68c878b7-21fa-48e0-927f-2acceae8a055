{"analysis_type": "advanced_obligations_extraction", "framework_version": "1.0", "timestamp": "2025-06-30T04:17:57.562682", "total_obligations_found": 0, "result": {"obligations": [{"category": "Service Delivery Management", "subcategory": "SDM-DR: Deliverables and Reports", "obligation_title": "Weekly Status Reporting", "short_description": "Deliver detailed project status reports every Friday.", "detailed_summary": "The Service Provider shall deliver a detailed project status report via email no later than 5:00 PM EST every Friday for the duration of the engagement. This report shall include a summary of work completed, milestones achieved, any risks or issues identified, and the planned activities for the following week.", "reference_clause": "4.1 Status Reporting", "impact_risk": "Performance and Relationship Risks - Failure to provide timely reports may lead to miscommunication and project delays.", "priority": "High"}, {"category": "Service Delivery Management", "subcategory": "SDM-IM: Incident Management", "obligation_title": "Breach Notification", "short_description": "Notify Client of any data breach within 12 hours.", "detailed_summary": "In the event of a security breach affecting the Client's data, the Service Provider is obligated to notify the Client's designated security contact in writing within 12 hours of discovering the breach.", "reference_clause": "7.3 Breach Notification", "impact_risk": "Regulatory Compliance Risks - Timely notification is critical to mitigate potential legal consequences.", "priority": "Critical"}, {"category": "Performance Management", "subcategory": "PM-PR: Performance Reporting", "obligation_title": "Annual Security Audit Reporting", "short_description": "Conduct annual security audits and report findings.", "detailed_summary": "The Service Provider agrees to conduct an annual third-party security audit and will provide the Client with a copy of the final audit report within 30 days of its completion.", "reference_clause": "7.2 Security Audits", "impact_risk": "Regulatory Compliance Risks - Ensures adherence to security standards and protects Client data.", "priority": "High"}, {"category": "Performance Management", "subcategory": "PM-PR: Performance Reporting", "obligation_title": "System Availability Guarantee", "short_description": "Guarantee system availability of at least 99.8%.", "detailed_summary": "The Service Provider guarantees that the hosted platform will maintain a System Availability of at least 99.8% during each calendar month. System Availability will be calculated as (Total Minutes in Month - Downtime Minutes) / (Total Minutes in Month).", "reference_clause": "8.1 System Availability", "impact_risk": "Performance and Relationship Risks - Failure to meet availability may lead to service disruptions and client dissatisfaction.", "priority": "High"}, {"category": "Compliance Management", "subcategory": "CM-INS: Insurance", "obligation_title": "Maintain Liability Insurance", "short_description": "Maintain general liability insurance coverage.", "detailed_summary": "For the term of this Agreement, the Service Provider shall maintain a policy of general liability insurance with a minimum coverage of $2,000,000 per occurrence.", "reference_clause": "11.4 Insurance", "impact_risk": "Commercial/Financial Risks - Ensures financial protection against potential claims.", "priority": "High"}, {"category": "Service Delivery Management", "subcategory": "SDM-OPS: Operations", "obligation_title": "Support Response Time for Urgent Requests", "short_description": "Provide initial response for urgent support requests within one hour.", "detailed_summary": "For any support requests classified as 'Urgent,' the Service Provider must provide an initial response within one (1) hour of the request being logged in the support portal.", "reference_clause": "8.2 Support Response Time", "impact_risk": "Performance and Relationship Risks - Timely responses are critical for maintaining client satisfaction.", "priority": "High"}]}}