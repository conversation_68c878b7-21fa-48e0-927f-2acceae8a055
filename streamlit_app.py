#!/usr/bin/env python3
"""
Streamlit Application for Contract Analysis using Azure AI Agent
"""

import streamlit as st
import os
import json
import tempfile
import time
from pathlib import Path
import pandas as pd
from datetime import datetime

# Import your existing modules
from utils_fr_PolicyReview import _extract_data_from_file, _initialize_AIAgentProject_client
from ToolsetForPolicyReview import (
    analyze_contract_for_obligations,
    extract_fields_using_template,
    analyze_document_and_extract_tables
)
from framework_validator import (
    validate_uploaded_guidelines_file,
    get_framework_requirements_summary
)

# Page configuration
st.set_page_config(
    page_title="Contract Analysis AI Agent",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .upload-section {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }
    .progress-section {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}
    if 'uploaded_files' not in st.session_state:
        st.session_state.uploaded_files = {}
    if 'analysis_in_progress' not in st.session_state:
        st.session_state.analysis_in_progress = False

def validate_json_file(uploaded_file):
    """Validate if uploaded file is a valid JSON"""
    try:
        content = uploaded_file.read()
        json.loads(content)
        uploaded_file.seek(0)  # Reset file pointer
        return True, "Valid JSON file"
    except json.JSONDecodeError as e:
        return False, f"Invalid JSON file: {str(e)}"
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def validate_framework_file(uploaded_file):
    """Validate if uploaded file matches Advanced_guidelines.json structure"""
    try:
        content = uploaded_file.read().decode('utf-8')
        uploaded_file.seek(0)  # Reset file pointer

        is_valid, errors, parsed_data = validate_uploaded_guidelines_file(content)

        if is_valid:
            return True, "Valid framework structure", parsed_data
        else:
            return False, f"Framework validation failed: {'; '.join(errors[:3])}", {}

    except Exception as e:
        return False, f"Error validating framework: {str(e)}", {}

def save_uploaded_file(uploaded_file, directory="temp_uploads"):
    """Save uploaded file to temporary directory"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    
    file_path = os.path.join(directory, uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    return file_path

def clean_template_name(template_name):
    """Clean template name by removing extra whitespaces"""
    if template_name:
        return template_name.strip()
    return ""

def display_progress_update(message, step_num, total_steps):
    """Display progress update with step information"""
    progress_percentage = step_num / total_steps
    st.progress(progress_percentage)
    st.markdown(f"""
    <div class="progress-section">
        <strong>Step {step_num}/{total_steps}:</strong> {message}
    </div>
    """, unsafe_allow_html=True)

def run_contract_analysis(contract_path, template_name, guidelines_path):
    """Run the complete contract analysis pipeline"""
    results = {}
    
    # Extract contract text
    progress_placeholder = st.empty()
    
    with progress_placeholder.container():
        display_progress_update("Extracting contract text...", 1, 4)
        time.sleep(1)
    
    try:
        contract_text = _extract_data_from_file(contract_path)
        if not contract_text or len(contract_text.strip()) < 100:
            raise ValueError("Contract text is too short or empty")
    except Exception as e:
        st.error(f"Error extracting contract text: {e}")
        return None
    
    # Step 1: Template-based extraction
    with progress_placeholder.container():
        display_progress_update("Performing template-based field extraction...", 2, 4)
        time.sleep(1)
    
    try:
        template_result = extract_fields_using_template(
            contract_text=contract_text,
            template_query=template_name,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['template_extraction'] = json.loads(template_result)
    except Exception as e:
        st.error(f"Error in template extraction: {e}")
        results['template_extraction'] = {"error": str(e)}
    
    # Step 2: Obligations analysis
    with progress_placeholder.container():
        display_progress_update("Analyzing contract obligations...", 3, 4)
        time.sleep(1)
    
    try:
        obligations_result = analyze_contract_for_obligations(
            contract_text=contract_text,
            guidelines_path=guidelines_path,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['obligations_analysis'] = json.loads(obligations_result)
    except Exception as e:
        st.error(f"Error in obligations analysis: {e}")
        results['obligations_analysis'] = {"error": str(e)}
    
    # Step 3: Table extraction (only for PDF files)
    with progress_placeholder.container():
        if contract_path.lower().endswith('.pdf'):
            display_progress_update("Extracting tables from PDF...", 4, 4)
            time.sleep(1)
            
            try:
                tables_result = analyze_document_and_extract_tables(
                    pdf_path=contract_path,
                    save_to_excel=True,
                    save_to_json=True,
                    output_dir="temp_uploads"
                )
                results['table_extraction'] = json.loads(tables_result)
            except Exception as e:
                st.error(f"Error in table extraction: {e}")
                results['table_extraction'] = {"error": str(e)}
        else:
            display_progress_update("Skipping table extraction (not a PDF file)", 4, 4)
            results['table_extraction'] = {
                "message": "Table extraction is only available for PDF files",
                "file_type": "text"
            }
    
    progress_placeholder.empty()
    return results

def main():
    """Main Streamlit application"""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">📄 Contract Analysis AI Agent</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for file uploads
    with st.sidebar:
        st.header("📁 Upload Files")
        
        # Contract file upload
        st.subheader("1. Contract File")
        contract_file = st.file_uploader(
            "Upload contract file",
            type=['pdf', 'txt'],
            help="Upload your contract in PDF or TXT format"
        )
        
        if contract_file:
            file_extension = Path(contract_file.name).suffix.lower()
            if file_extension == '.txt':
                st.markdown("""
                <div class="warning-message">
                    <strong>Note:</strong> TXT files uploaded - Table extraction will be skipped.
                </div>
                """, unsafe_allow_html=True)
        
        # Template type input
        st.subheader("2. Contract Template Type")
        template_name = st.text_input(
            "Enter template type",
            placeholder="e.g., MSA, Statement of Work, NDA",
            help="Specify the type of contract template for field extraction"
        )
        
        # Guidelines framework selection
        st.subheader("3. Guidelines Framework")

        # Prominently display default option
        st.markdown("""
        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 5px solid #28a745;">
            <strong>🎯 Recommended: Advanced EYGS SRM D&O Framework</strong><br>
            Comprehensive framework with 5 obligation categories, risk classification, and priority matrix.
        </div>
        """, unsafe_allow_html=True)

        use_default_guidelines = st.radio(
            "Select Guidelines Framework:",
            options=[
                "Use Advanced EYGS SRM D&O Framework (Recommended)",
                "Upload Custom Framework (Advanced Users Only)"
            ],
            index=0,
            help="The default framework is recommended for most users"
        )

        guidelines_file = None
        custom_guidelines_valid = False

        if "Custom Framework" in use_default_guidelines:
            # Show warning for custom upload
            st.warning("""
            ⚠️ **Advanced Users Only**: Custom frameworks must exactly match the Advanced_guidelines.json structure.
            Incorrect structure will cause the system to fall back to the default framework.
            """)

            # Show requirements
            with st.expander("📋 View Required Framework Structure"):
                st.code(get_framework_requirements_summary(), language="text")

            guidelines_file = st.file_uploader(
                "Upload custom guidelines JSON file",
                type=['json'],
                help="Must match the exact structure of Advanced_guidelines.json"
            )

            if guidelines_file:
                # Validate both JSON and framework structure
                file_content = guidelines_file.read().decode('utf-8')
                guidelines_file.seek(0)  # Reset file pointer

                is_valid, errors, parsed_data = validate_uploaded_guidelines_file(file_content)

                if is_valid:
                    st.success("✅ Valid custom framework uploaded and validated")
                    custom_guidelines_valid = True
                else:
                    st.error("❌ Custom framework validation failed:")
                    for error in errors[:5]:  # Show first 5 errors
                        st.error(f"• {error}")
                    if len(errors) > 5:
                        st.error(f"... and {len(errors) - 5} more errors")

                    st.warning("🔄 System will automatically use the default Advanced framework instead.")
                    custom_guidelines_valid = False
        else:
            st.success("✅ Using Advanced EYGS SRM D&O Framework")
            custom_guidelines_valid = True  # Default is always valid
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🚀 Analysis Control")
        
        # Check if all required inputs are available
        using_default = "Advanced EYGS" in use_default_guidelines
        guidelines_ready = using_default or (guidelines_file is not None and custom_guidelines_valid)
        all_inputs_ready = contract_file is not None and guidelines_ready and template_name.strip()

        if not all_inputs_ready:
            missing_items = []
            if not contract_file:
                missing_items.append("Contract file")
            if not guidelines_ready:
                if using_default:
                    missing_items.append("Default framework (should be available)")
                else:
                    missing_items.append("Valid custom guidelines framework")
            if not template_name.strip():
                missing_items.append("Template type")

            st.warning(f"Please provide: {', '.join(missing_items)}")
        
        # Analysis button
        analyze_button = st.button(
            "🔍 Start Analysis",
            disabled=not all_inputs_ready or st.session_state.analysis_in_progress,
            use_container_width=True
        )

        if analyze_button and all_inputs_ready:
            st.session_state.analysis_in_progress = True
            st.session_state.analysis_complete = False
            
            # Save uploaded files
            try:
                contract_path = save_uploaded_file(contract_file)

                # Handle guidelines path with fallback mechanism
                using_default = "Advanced EYGS" in use_default_guidelines

                if using_default:
                    guidelines_path = "Advanced_guidelines.json"
                    st.info("📋 Using Advanced EYGS SRM D&O Framework")
                else:
                    # Custom framework - check if validation passed
                    if custom_guidelines_valid and guidelines_file is not None:
                        guidelines_path = save_uploaded_file(guidelines_file)
                        st.info("📋 Using validated custom framework")
                    else:
                        # Fallback to default
                        guidelines_path = "Advanced_guidelines.json"
                        st.warning("⚠️ Custom framework validation failed. Falling back to Advanced EYGS SRM D&O Framework.")

                cleaned_template_name = clean_template_name(template_name)
                
                st.session_state.uploaded_files = {
                    'contract_path': contract_path,
                    'guidelines_path': guidelines_path,
                    'template_name': cleaned_template_name,
                    'contract_filename': contract_file.name
                }
                
                # Run analysis
                with st.spinner("Running contract analysis..."):
                    results = run_contract_analysis(
                        contract_path, 
                        cleaned_template_name, 
                        guidelines_path
                    )
                
                if results:
                    st.session_state.analysis_results = results
                    st.session_state.analysis_complete = True
                    st.markdown("""
                    <div class="success-message">
                        <strong>Analysis completed successfully 🎉</strong> Check the results in the tabs below.
                    </div>
                    """, unsafe_allow_html=True)
                
            except Exception as e:
                st.error(f"Error during analysis: {e}")
            finally:
                st.session_state.analysis_in_progress = False
    
    with col2:
        st.header("📊 Upload Status")

        # Display upload status with better formatting
        status_items = [
            ("Contract File", contract_file is not None, contract_file.name if contract_file else "Not uploaded"),
            ("Template Type", bool(template_name.strip()), template_name.strip() if template_name.strip() else "Not specified"),
            ("Guidelines File", guidelines_file is not None, guidelines_file.name if guidelines_file else "Not uploaded")
        ]

        for item_name, is_uploaded, filename in status_items:
            if is_uploaded:
                st.markdown(f"""
                <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #28a745;">
                    <strong style="color: #155724;">✅ {item_name}</strong><br>
                    <small style="color: #155724;">{filename}</small>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #dc3545;">
                    <strong style="color: #721c24;">❌ {item_name}</strong><br>
                    <small style="color: #721c24;">{filename}</small>
                </div>
                """, unsafe_allow_html=True)

        # Show file type information
        if contract_file:
            file_extension = Path(contract_file.name).suffix.lower()
            if file_extension == '.pdf':
                st.info("📄 PDF file - Full analysis including table extraction")
            elif file_extension == '.txt':
                st.warning("📝 Text file - Table extraction will be skipped")

        # Show analysis status
        if st.session_state.analysis_in_progress:
            st.info("⏳ Analysis in progress...")
    
    # Results section
    if st.session_state.analysis_complete and st.session_state.analysis_results:
        st.markdown("---")
        st.header("📋 Analysis Results")
        
        # Create tabs for different analysis results
        tab1, tab2, tab3 = st.tabs(["📝 Template Extraction", "⚖️ Obligations Analysis", "📊 Tables"])
        
        results = st.session_state.analysis_results
        
        with tab1:
            if 'template_extraction' in results:
                template_data = results['template_extraction']

                if 'error' in template_data:
                    st.error(f"❌ Error in template extraction: {template_data['error']}")
                else:
                    # Check for success response structure
                    if template_data.get('success') and 'data' in template_data:
                        # Success response structure: {"success": true, "data": {...}}
                        data = template_data['data']
                        if 'result' in data:
                            extracted_fields = data['result']
                            template_used = data.get('template_used', 'Unknown')
                        else:
                            extracted_fields = data
                            template_used = 'Unknown'

                        # Create two columns for side-by-side layout
                        col1, col2 = st.columns([1.2, 0.8])

                        with col1:
                            # Show clean summary table
                            st.markdown("### 📊 Extracted Contract Information")
                            display_data = []
                            for field_name, field_value in extracted_fields.items():
                                display_data.append({
                                    "Field": str(field_name.replace('_', ' ').title()),
                                    "Value": str(field_value if field_value else 'Not specified')
                                })

                            df = pd.DataFrame(display_data)
                            # Ensure all values are strings to avoid Arrow serialization issues
                            df["Field"] = df["Field"].astype(str)
                            df["Value"] = df["Value"].astype(str)
                            st.dataframe(df, use_container_width=True, hide_index=True)

                        with col2:
                            # Show template used for extraction
                            st.markdown("### 📋 Template Information")

                            # Get template details from the data
                            template_system_prompt = data.get('template_system_prompt', 'Not available')
                            template_fields = data.get('template_fields', [])

                            # Show basic template info
                            st.info(f"**Template:** {template_used}")

                            # Show detailed template information in collapsible sections
                            with st.expander("🔧 System Prompt", expanded=False):
                                st.markdown("**System Prompt used for extraction:**")
                                st.text_area("System Prompt", value=template_system_prompt, height=120, disabled=True, key="system_prompt_display", label_visibility="collapsed")

                            with st.expander("📝 Fields to Extract", expanded=False):
                                if template_fields:
                                    st.markdown("**Fields defined in the template:**")

                                    # Create a table showing field definitions
                                    fields_data = []
                                    for field in template_fields:
                                        fields_data.append({
                                            "Field Name": str(field.get('field_name', 'Unknown')),
                                            "Description": str(field.get('prompt', 'No description available'))
                                        })

                                    if fields_data:
                                        fields_df = pd.DataFrame(fields_data)
                                        # Ensure all values are strings to avoid Arrow serialization issues
                                        for col in fields_df.columns:
                                            fields_df[col] = fields_df[col].astype(str)
                                        st.dataframe(fields_df, use_container_width=True, hide_index=True)
                                    else:
                                        st.warning("No field definitions available")
                                else:
                                    st.warning("No template fields information available")

                        # Show raw JSON in an expander for technical users
                        with st.expander("🔧 View Raw Data (Technical)"):
                            st.json(template_data)
                    else:
                        st.warning("⚠️ No template extraction results found")
                        with st.expander("🔧 View Raw Response"):
                            st.json(template_data)
        
        with tab2:
            if 'obligations_analysis' in results:
                obligations_data = results['obligations_analysis']
                if 'error' in obligations_data:
                    st.error(f"❌ Error in obligations analysis: {obligations_data['error']}")
                else:
                    # Check for success response structure
                    if obligations_data.get('success') and 'data' in obligations_data:
                        # Success response structure: {"success": true, "data": {"result": {"obligations": [...]}}}
                        data = obligations_data['data']

                        # Extract obligations from nested structure
                        obligations_list = []
                        if 'result' in data:
                            result = data['result']
                            if isinstance(result, dict) and 'obligations' in result:
                                obligations_list = result['obligations']
                            elif isinstance(result, list):
                                obligations_list = result
                        elif isinstance(data, list):
                            obligations_list = data

                        if isinstance(obligations_list, list) and obligations_list:
                            st.success(f"✅ Found **{len(obligations_list)}** obligations in the contract")

                            # Check if this is the new advanced format
                            if obligations_list and 'obligation_title' in obligations_list[0]:
                                # New advanced format display
                                st.markdown("### 📊 Contract Obligations Analysis")

                                # Create detailed obligations table
                                detailed_table_data = []
                                for obligation in obligations_list:
                                    # Combine title and short description
                                    title_desc = f"**{obligation.get('obligation_title', 'N/A')}**\n{obligation.get('short_description', 'N/A')}"

                                    detailed_table_data.append({
                                        "Category": str(obligation.get('category', 'N/A')),
                                        "Subcategory": str(obligation.get('subcategory', 'N/A')),
                                        "Obligation Title + Description": str(title_desc),
                                        "Detailed Summary": str(obligation.get('detailed_summary', 'N/A')),
                                        "Reference Clause": str(obligation.get('reference_clause', 'N/A')),
                                        "Impact/Risk": str(obligation.get('impact_risk', 'N/A')),
                                        "Priority": str(obligation.get('priority', 'N/A'))
                                    })

                                detailed_df = pd.DataFrame(detailed_table_data)
                                # Ensure all values are strings to avoid Arrow serialization issues
                                for col in detailed_df.columns:
                                    detailed_df[col] = detailed_df[col].astype(str)

                                st.dataframe(detailed_df, use_container_width=True, hide_index=True)

                                # Show summary by category
                                with st.expander("📈 Summary by Category"):
                                    category_summary = {}
                                    for obligation in obligations_list:
                                        category = obligation.get('category', 'Uncategorized')
                                        priority = obligation.get('priority', 'Unknown')

                                        if category not in category_summary:
                                            category_summary[category] = {'total': 0, 'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}

                                        category_summary[category]['total'] += 1
                                        if priority in category_summary[category]:
                                            category_summary[category][priority] += 1

                                    summary_data = []
                                    for category, counts in category_summary.items():
                                        summary_data.append({
                                            "Category": str(category),
                                            "Total": str(counts['total']),
                                            "Critical": str(counts['Critical']),
                                            "High": str(counts['High']),
                                            "Medium": str(counts['Medium']),
                                            "Low": str(counts['Low'])
                                        })

                                    summary_df = pd.DataFrame(summary_data)
                                    for col in summary_df.columns:
                                        summary_df[col] = summary_df[col].astype(str)
                                    st.dataframe(summary_df, use_container_width=True, hide_index=True)

                            else:
                                # Legacy format display (fallback)
                                obligations_by_category = {}
                                for obligation in obligations_list:
                                    category = obligation.get('category', 'Uncategorized')
                                    if category not in obligations_by_category:
                                        obligations_by_category[category] = []
                                    obligations_by_category[category].append(obligation)

                                st.markdown("### 📊 Obligations Summary")
                                summary_table_data = []
                                for category, category_obligations in obligations_by_category.items():
                                    all_texts = []
                                    for obligation in category_obligations:
                                        text = obligation.get('text', 'N/A')
                                        all_texts.append(f"• {text}")

                                    combined_text = "\n".join(all_texts)

                                    summary_table_data.append({
                                        "Category": str(category),
                                        "Count": str(len(category_obligations)),
                                        "Obligations": str(combined_text)
                                    })

                                summary_df = pd.DataFrame(summary_table_data)
                                for col in summary_df.columns:
                                    summary_df[col] = summary_df[col].astype(str)
                                st.dataframe(summary_df, use_container_width=True, hide_index=True)

                            # Raw data for technical users
                            with st.expander("🔧 View Raw Data (Technical)"):
                                st.json(obligations_data)
                        else:
                            st.warning("⚠️ No obligations found in the contract")
                            with st.expander("🔧 View Raw Response"):
                                st.json(obligations_data)
                    else:
                        st.warning("⚠️ No obligations analysis results found")
                        with st.expander("🔧 View Raw Response"):
                            st.json(obligations_data)
        
        with tab3:
            if 'table_extraction' in results:
                table_data = results['table_extraction']

                # Handle text files (no table extraction)
                if 'message' in table_data and table_data.get('file_type') == 'text':
                    st.markdown("""
                    <div style="background-color: #fff3e0; padding: 20px; border-radius: 10px; border-left: 5px solid #ff9800; color: #e65100;">
                        <h4 style="color: #e65100; margin-top: 0;">ℹ️ Why no tables?</h4>
                        <p style="color: #bf360c;">Your uploaded file was in <strong>text format (.txt)</strong>. Table extraction requires PDF files because:</p>
                        <ul style="color: #bf360c;">
                            <li>📄 PDF files preserve table structure and layout</li>
                            <li>🔍 Our AI can detect table boundaries in PDF format</li>
                            <li>📝 Text files don't contain table formatting information</li>
                        </ul>
                        <p style="color: #bf360c;"><strong>💡 Tip:</strong> If your contract contains tables, try uploading it as a PDF file instead.</p>
                    </div>
                    """, unsafe_allow_html=True)

                # Handle errors
                elif 'error' in table_data:
                    st.error(f"❌ Error in table extraction: {table_data['error']}")
                    st.markdown("**Possible solutions:**")
                    st.markdown("- Check if the PDF file is not corrupted")
                    st.markdown("- Ensure the PDF contains actual tables (not just text)")
                    st.markdown("- Try with a different PDF file")

                # Handle successful extraction
                else:
                    # Check for success response structure
                    if table_data.get('success') and 'data' in table_data:
                        # Success response structure: {"success": true, "data": {...}}
                        data = table_data['data']
                        if 'tables' in data:
                            tables_info = data['tables']
                            total_pages = data.get('total_pages', 0)
                            pages_with_tables = data.get('pages_with_tables', 0)
                        else:
                            tables_info = []
                            total_pages = 0
                            pages_with_tables = 0

                        # Calculate total tables
                        total_tables = sum(len(page.get('tables', [])) for page in tables_info)

                        if tables_info and total_tables > 0:
                            # Create side-by-side layout: tables on left (wider), summary on right
                            table_col, summary_col = st.columns([3, 1])

                            with table_col:
                                # Create collapsible sections for extracted tables with preview
                                for page_data in tables_info:
                                    page_num = page_data.get('page_number', 'Unknown')
                                    tables = page_data.get('tables', [])

                                    if tables:  # Only show pages that have tables
                                        for table_idx, table in enumerate(tables):
                                            rows = table.get('rows', 0)
                                            columns = table.get('columns', 0)
                                            table_data_content = table.get('data', [])

                                            # Create preview text
                                            preview_text = f"Page {page_num}, Table {table_idx + 1} - {rows} rows × {columns} columns"
                                            if table_data_content and len(table_data_content) > 0:
                                                # Show first few cells as preview
                                                first_row = table_data_content[0] if table_data_content else []
                                                preview_cells = first_row[:3] if first_row else []
                                                if preview_cells:
                                                    preview_text += f" | Preview: {', '.join(str(cell)[:20] + '...' if len(str(cell)) > 20 else str(cell) for cell in preview_cells if cell)}"

                                            with st.expander(f"📊 {preview_text}", expanded=False):
                                                if table_data_content and len(table_data_content) > 0:
                                                    try:
                                                        # Convert to DataFrame for better display
                                                        if len(table_data_content) > 1:
                                                            # Use first row as headers
                                                            headers = table_data_content[0]
                                                            data_rows = table_data_content[1:]

                                                            # Clean headers (remove empty or None values)
                                                            clean_headers = []
                                                            for i, header in enumerate(headers):
                                                                if header and str(header).strip():
                                                                    clean_headers.append(str(header).strip())
                                                                else:
                                                                    clean_headers.append(f"Column {i+1}")

                                                            df = pd.DataFrame(data_rows, columns=clean_headers)

                                                            # Remove completely empty rows
                                                            df = df.dropna(how='all')

                                                            if not df.empty:
                                                                st.dataframe(df, use_container_width=True, hide_index=True)
                                                                st.caption(f"📊 Table contains {len(df)} data rows and {len(df.columns)} columns")
                                                            else:
                                                                st.warning("⚠️ Table appears to be empty after processing")
                                                        else:
                                                            # Single row table
                                                            df = pd.DataFrame([table_data_content[0]], columns=[f"Column {i+1}" for i in range(len(table_data_content[0]))])
                                                            st.dataframe(df, use_container_width=True, hide_index=True)
                                                            st.caption("📊 Single row table")

                                                    except Exception as e:
                                                        st.warning(f"⚠️ Could not format table properly. Showing raw data:")
                                                        # Fallback to raw display
                                                        for row_idx, row in enumerate(table_data_content):
                                                            st.write(f"**Row {row_idx + 1}:** {row}")
                                                else:
                                                    st.warning("⚠️ This table appears to be empty or could not be extracted properly")

                            with summary_col:
                                # Show summary statistics on the right
                                st.markdown("### 📈 Summary")
                                st.metric("📄 Total Pages", total_pages)
                                st.metric("📊 Pages with Tables", pages_with_tables)
                                st.metric("🗂️ Total Tables", total_tables)



                        else:
                            # No tables found in PDF
                            st.info("📄 **No tables found in the PDF document**")
                            st.markdown("""
                            <div style="background-color: #fff3e0; padding: 20px; border-radius: 10px; border-left: 5px solid #ff9800;">
                                <h4 style="color: #f57c00; margin-top: 0;">🔍 Why no tables detected?</h4>
                                <p>The PDF was successfully processed, but no table structures were detected. This could be because:</p>
                                <ul>
                                    <li>📝 The document contains only text content</li>
                                    <li>🖼️ Tables might be embedded as images (scanned documents)</li>
                                    <li>📋 Content is formatted as lists rather than tables</li>
                                    <li>🎨 Tables use non-standard formatting that wasn't recognized</li>
                                </ul>
                                <p><strong>💡 Note:</strong> This is normal for many contracts that don't contain tabular data.</p>
                            </div>
                            """, unsafe_allow_html=True)

                        # Raw data for technical users
                        with st.expander("🔧 View Raw Data (Technical)"):
                            st.json(table_data)

                    else:
                        st.warning("⚠️ No table extraction results found")
                        with st.expander("🔧 View Raw Response"):
                            st.json(table_data)

if __name__ == "__main__":
    main()
