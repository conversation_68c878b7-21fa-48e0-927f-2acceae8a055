#!/usr/bin/env python3
"""
Optimized Streamlit Application for Contract Analysis using Azure AI Agent
"""

import streamlit as st
import os
import json
import time
from pathlib import Path
import pandas as pd

# Import existing modules - reuse existing functionality
from utils_fr_PolicyReview import _extract_data_from_file
from ToolsetForPolicyReview import (
    analyze_contract_for_obligations,
    extract_fields_using_template,
    analyze_document_and_extract_tables
)
from framework_validator import validate_uploaded_guidelines_file

# Page configuration
st.set_page_config(
    page_title="Contract Analysis AI Agent",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analysis_in_progress' not in st.session_state:
    st.session_state.analysis_in_progress = False
if 'analysis_complete' not in st.session_state:
    st.session_state.analysis_complete = False
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = None

# Custom CSS (simplified)
st.markdown("""
<style>
    .main-header { font-size: 2.5rem; color: #1f77b4; text-align: center; margin-bottom: 2rem; }
    .success-message { background-color: #d4edda; padding: 1rem; border-radius: 5px; border-left: 5px solid #28a745; }
    .warning-message { background-color: #fff3cd; padding: 1rem; border-radius: 5px; border-left: 5px solid #ffc107; }
</style>
""", unsafe_allow_html=True)

# Utility functions (simplified and reused)
def save_uploaded_file(uploaded_file, directory="temp_uploads"):
    """Save uploaded file to temporary directory"""
    os.makedirs(directory, exist_ok=True)
    file_path = os.path.join(directory, uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    return file_path

def validate_custom_framework(guidelines_file):
    """Validate custom framework file"""
    if not guidelines_file:
        return False, []
    
    try:
        file_content = guidelines_file.read().decode('utf-8')
        guidelines_file.seek(0)  # Reset file pointer
        is_valid, errors, _ = validate_uploaded_guidelines_file(file_content)
        return is_valid, errors
    except Exception as e:
        return False, [f"Error validating framework: {str(e)}"]

def run_analysis_pipeline(contract_path, template_name, guidelines_path):
    """Run the complete contract analysis pipeline"""
    results = {}
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Step 1: Extract contract text
        status_text.text("Extracting contract text...")
        progress_bar.progress(25)
        contract_text = _extract_data_from_file(contract_path)
        if not contract_text or len(contract_text.strip()) < 100:
            raise ValueError("Contract text is too short or empty")
        
        # Step 2: Template extraction
        status_text.text("Performing template-based field extraction...")
        progress_bar.progress(50)
        template_result = extract_fields_using_template(
            contract_text=contract_text,
            template_query=template_name,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['template_extraction'] = json.loads(template_result)
        
        # Step 3: Obligations analysis
        status_text.text("Analyzing contract obligations...")
        progress_bar.progress(75)
        obligations_result = analyze_contract_for_obligations(
            contract_text=contract_text,
            guidelines_path=guidelines_path,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['obligations_analysis'] = json.loads(obligations_result)
        
        # Step 4: Table extraction (PDF only)
        if contract_path.lower().endswith('.pdf'):
            status_text.text("Extracting tables from PDF...")
            tables_result = analyze_document_and_extract_tables(
                pdf_path=contract_path,
                save_to_excel=True,
                save_to_json=True,
                output_dir="temp_uploads"
            )
            results['table_extraction'] = json.loads(tables_result)
        else:
            results['table_extraction'] = {
                "message": "Table extraction is only available for PDF files",
                "file_type": "text"
            }
        
        progress_bar.progress(100)
        status_text.text("Analysis completed successfully!")
        return results
        
    except Exception as e:
        st.error(f"Error in analysis: {e}")
        return None

def display_template_results(template_data):
    """Display template extraction results"""
    if 'result' in template_data:
        extracted_fields = template_data['result']
        display_data = []
        for field_name, field_value in extracted_fields.items():
            display_data.append({
                "Field": str(field_name.replace('_', ' ').title()),
                "Value": str(field_value if field_value else 'Not specified')
            })
        
        df = pd.DataFrame(display_data)
        df["Field"] = df["Field"].astype(str)
        df["Value"] = df["Value"].astype(str)
        st.dataframe(df, use_container_width=True, hide_index=True)

def display_obligations_results(obligations_data):
    """Display obligations analysis results"""
    if 'result' in obligations_data:
        obligations_list = obligations_data['result']
        
        if isinstance(obligations_list, list) and obligations_list:
            st.success(f"✅ Found **{len(obligations_list)}** obligations in the contract")
            
            # Check if this is the new advanced format
            if obligations_list and 'obligation_title' in obligations_list[0]:
                # Advanced format display
                detailed_table_data = []
                for obligation in obligations_list:
                    title_desc = f"**{obligation.get('obligation_title', 'N/A')}**\n{obligation.get('short_description', 'N/A')}"
                    
                    detailed_table_data.append({
                        "Category": str(obligation.get('category', 'N/A')),
                        "Subcategory": str(obligation.get('subcategory', 'N/A')),
                        "Obligation Title + Description": str(title_desc),
                        "Detailed Summary": str(obligation.get('detailed_summary', 'N/A')),
                        "Reference Clause": str(obligation.get('reference_clause', 'N/A')),
                        "Impact/Risk": str(obligation.get('impact_risk', 'N/A')),
                        "Priority": str(obligation.get('priority', 'N/A'))
                    })

                detailed_df = pd.DataFrame(detailed_table_data)
                for col in detailed_df.columns:
                    detailed_df[col] = detailed_df[col].astype(str)
                
                st.dataframe(detailed_df, use_container_width=True, hide_index=True)
            else:
                # Legacy format fallback
                st.json(obligations_list)

def display_tables_results(table_data):
    """Display table extraction results"""
    if 'message' in table_data and table_data.get('file_type') == 'text':
        st.info("📝 Table extraction is only available for PDF files")
    elif 'result' in table_data and table_data['result']:
        tables = table_data['result']
        if tables:
            st.success(f"✅ Found {len(tables)} tables in the document")
            for i, table in enumerate(tables, 1):
                st.subheader(f"Table {i}")
                if 'data' in table:
                    df = pd.DataFrame(table['data'])
                    st.dataframe(df, use_container_width=True)
        else:
            st.info("No tables found in the document")

# Main application
def main():
    st.markdown('<h1 class="main-header">📄 Contract Analysis AI Agent</h1>', unsafe_allow_html=True)
    
    # Sidebar for inputs
    with st.sidebar:
        st.header("📁 Upload Files")
        
        # Contract file upload
        st.subheader("1. Contract File")
        contract_file = st.file_uploader(
            "Upload contract file",
            type=['pdf', 'txt'],
            help="Upload your contract in PDF or TXT format"
        )
        
        # Template type input
        st.subheader("2. Contract Template Type")
        template_name = st.text_input(
            "Enter template type",
            placeholder="e.g., MSA, Statement of Work, NDA",
            help="Specify the type of contract template for field extraction"
        )
        
        # Guidelines framework selection (simplified)
        st.subheader("3. Guidelines Framework")
        use_default_guidelines = st.checkbox(
            "Use Advanced EYGS SRM D&O Framework (Recommended)", 
            value=True,
            help="Uses the comprehensive Advanced_guidelines.json framework"
        )
        
        guidelines_file = None
        if not use_default_guidelines:
            guidelines_file = st.file_uploader(
                "Upload custom guidelines JSON file",
                type=['json'],
                help="Must match Advanced_guidelines.json structure"
            )
            
            if guidelines_file:
                is_valid, errors = validate_custom_framework(guidelines_file)
                if is_valid:
                    st.success("✅ Valid custom framework")
                else:
                    st.error("❌ Invalid framework - will use default")
                    guidelines_file = None
        
        if use_default_guidelines or guidelines_file is None:
            st.success("✅ Using Advanced EYGS SRM D&O Framework")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🚀 Analysis Control")
        
        # Check inputs
        all_inputs_ready = (
            contract_file is not None and 
            template_name.strip() and 
            (use_default_guidelines or guidelines_file is not None)
        )
        
        if not all_inputs_ready:
            missing = []
            if not contract_file: missing.append("Contract file")
            if not template_name.strip(): missing.append("Template type")
            if not use_default_guidelines and not guidelines_file: missing.append("Guidelines framework")
            st.warning(f"Please provide: {', '.join(missing)}")
        
        # Analysis button
        if st.button("🔍 Start Analysis", disabled=not all_inputs_ready or st.session_state.analysis_in_progress):
            if all_inputs_ready:
                st.session_state.analysis_in_progress = True
                st.session_state.analysis_complete = False
                
                # Save files and run analysis
                contract_path = save_uploaded_file(contract_file)
                
                if use_default_guidelines:
                    guidelines_path = "Advanced_guidelines.json"
                else:
                    guidelines_path = save_uploaded_file(guidelines_file)
                
                # Run analysis
                with st.spinner("Running contract analysis..."):
                    results = run_analysis_pipeline(contract_path, template_name.strip(), guidelines_path)
                
                if results:
                    st.session_state.analysis_results = results
                    st.session_state.analysis_complete = True
                    st.success("🎉 Analysis completed successfully!")
                
                st.session_state.analysis_in_progress = False
    
    with col2:
        st.header("📋 Status")
        if contract_file:
            file_ext = Path(contract_file.name).suffix.lower()
            if file_ext == '.pdf':
                st.info("📄 PDF - Full analysis including tables")
            else:
                st.warning("📝 Text - Table extraction skipped")
        
        if st.session_state.analysis_in_progress:
            st.info("⏳ Analysis in progress...")
    
    # Results section
    if st.session_state.analysis_complete and st.session_state.analysis_results:
        st.markdown("---")
        st.header("📊 Analysis Results")
        
        tab1, tab2, tab3 = st.tabs(["📝 Template Extraction", "⚖️ Obligations Analysis", "📊 Tables"])
        
        results = st.session_state.analysis_results
        
        with tab1:
            if 'template_extraction' in results:
                display_template_results(results['template_extraction'])
        
        with tab2:
            if 'obligations_analysis' in results:
                display_obligations_results(results['obligations_analysis'])
        
        with tab3:
            if 'table_extraction' in results:
                display_tables_results(results['table_extraction'])

if __name__ == "__main__":
    main()
